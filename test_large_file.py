#!/usr/bin/env python3
"""
测试大文件上传功能
"""

import requests
import os
import time

def test_large_file_upload(file_path="shipin.mp4", server_url="http://localhost:8001"):
    """测试大文件上传"""
    print("=" * 60)
    print("测试大文件上传功能")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print(f"✗ 文件不存在: {file_path}")
        return
    
    file_size = os.path.getsize(file_path)
    print(f"文件: {file_path}")
    print(f"大小: {file_size / (1024*1024):.2f} MB")
    
    try:
        print(f"正在上传到 {server_url}/api/upload...")
        start_time = time.time()
        
        with open(file_path, "rb") as f:
            files = {"file": (file_path, f, "video/mp4")}
            response = requests.post(
                f"{server_url}/api/upload", 
                files=files, 
                timeout=600  # 10分钟超时
            )
        
        upload_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            print("✓ 上传成功!")
            print(f"上传时间: {upload_time:.2f} 秒")
            print(f"转录ID: {result['id']}")
            print(f"文件名: {result['filename']}")
            print(f"状态: {result.get('status', 'unknown')}")
            print(f"时长: {result.get('duration', 'unknown')}")
            
            if result.get('transcription_file'):
                print(f"转录文件: {result['transcription_file']}")
            
            print("\n转录结果预览:")
            transcription = result.get('transcription', '')
            if len(transcription) > 500:
                print(transcription[:500] + "...")
            else:
                print(transcription)
                
        else:
            print(f"✗ 上传失败: {response.status_code}")
            print(f"错误信息: {response.text}")
    
    except requests.exceptions.Timeout:
        print("✗ 上传超时")
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到服务器，请确保服务器正在运行")
        print("启动命令: python3 -m uvicorn backend.main:app --host 0.0.0.0 --port 8001")
    except Exception as e:
        print(f"✗ 测试失败: {e}")

def test_history_and_download(server_url="http://localhost:8001"):
    """测试历史记录和下载功能"""
    print("\n" + "=" * 60)
    print("测试历史记录和下载功能")
    print("=" * 60)
    
    try:
        # 获取历史记录
        response = requests.get(f"{server_url}/api/history")
        if response.status_code == 200:
            history = response.json()
            print(f"✓ 历史记录数量: {len(history)}")
            
            for i, item in enumerate(history[:3], 1):  # 显示前3条
                print(f"\n{i}. {item['filename']}")
                print(f"   ID: {item['id']}")
                print(f"   状态: {item.get('status', 'unknown')}")
                print(f"   时间: {item['created_at']}")
                print(f"   大小: {item['file_size'] / (1024*1024):.2f} MB")
                
                if item.get('transcription_file'):
                    print(f"   转录文件: {item['transcription_file']}")
                    
                    # 测试下载
                    try:
                        download_response = requests.get(f"{server_url}/api/download/{item['id']}")
                        if download_response.status_code == 200:
                            print(f"   ✓ 下载测试成功")
                        else:
                            print(f"   ✗ 下载测试失败: {download_response.status_code}")
                    except Exception as e:
                        print(f"   ✗ 下载测试异常: {e}")
        else:
            print(f"✗ 获取历史记录失败: {response.status_code}")
    
    except Exception as e:
        print(f"✗ 测试失败: {e}")

def check_transcription_files():
    """检查转录文件目录"""
    print("\n" + "=" * 60)
    print("检查转录文件目录")
    print("=" * 60)
    
    transcription_dir = "transcriptions"
    if os.path.exists(transcription_dir):
        files = os.listdir(transcription_dir)
        print(f"✓ 转录文件目录存在: {transcription_dir}")
        print(f"✓ 文件数量: {len(files)}")
        
        for file in files:
            file_path = os.path.join(transcription_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"  - {file} ({file_size} bytes)")
    else:
        print(f"✗ 转录文件目录不存在: {transcription_dir}")

if __name__ == "__main__":
    # 检查服务器状态
    server_url = "http://localhost:8001"
    
    print("检查服务器状态...")
    try:
        response = requests.get(server_url, timeout=5)
        if response.status_code == 200:
            print("✓ 服务器运行正常")
        else:
            print(f"⚠ 服务器响应异常: {response.status_code}")
    except:
        print("✗ 服务器未运行，请先启动服务器:")
        print("python3 -m uvicorn backend.main:app --host 0.0.0.0 --port 8001")
        exit(1)
    
    # 运行测试
    test_large_file_upload()
    test_history_and_download()
    check_transcription_files()
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("现在可以在浏览器中访问: http://localhost:8001")
    print("上传您的shipin.mp4文件进行测试")
    print("=" * 60)
