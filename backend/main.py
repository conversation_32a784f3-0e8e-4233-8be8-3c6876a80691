from fastapi import FastAPI, File, UploadFile, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import os
import tempfile
import uuid
from datetime import datetime
from typing import List, Optional
import json
from sqlalchemy import create_engine, Column, String, DateTime, Text, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
import aiofiles

# 尝试导入WhisperX，如果失败则使用模拟版本
try:
    import whisperx
    import torch
    WHISPERX_AVAILABLE = True
except ImportError:
    WHISPERX_AVAILABLE = False
    print("警告: WhisperX未安装，将使用模拟转录功能进行演示")

# 数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./transcriptions.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# 数据库模型
class Transcription(Base):
    __tablename__ = "transcriptions"
    
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String, index=True)
    original_filename = Column(String)
    transcription_text = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    file_size = Column(Integer)
    duration = Column(String)

Base.metadata.create_all(bind=engine)

# 依赖注入
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

app = FastAPI(title="Audio/Video Transcription API", version="1.0.0")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
app.mount("/static", StaticFiles(directory="frontend"), name="static")

# 全局变量存储WhisperX模型
whisper_model = None
device = None

def load_whisper_model():
    """加载WhisperX模型"""
    global whisper_model, device
    if not WHISPERX_AVAILABLE:
        return None

    if whisper_model is None:
        device = "cuda" if torch.cuda.is_available() else "cpu"
        compute_type = "float16" if device == "cuda" else "int8"

        # 加载模型
        whisper_model = whisperx.load_model("base", device, compute_type=compute_type)

    return whisper_model

def mock_transcribe(file_path):
    """模拟转录功能，用于演示"""
    filename = os.path.basename(file_path)
    return {
        "language": "zh",
        "segments": [
            {
                "start": 0.0,
                "end": 5.0,
                "text": f"这是对文件 {filename} 的模拟转录结果。"
            },
            {
                "start": 5.0,
                "end": 10.0,
                "text": "由于WhisperX未安装，这里显示的是演示内容。"
            },
            {
                "start": 10.0,
                "end": 15.0,
                "text": "请安装WhisperX以获得真实的语音识别功能。"
            }
        ]
    }

@app.on_event("startup")
async def startup_event():
    """应用启动时加载模型"""
    if WHISPERX_AVAILABLE:
        print("Loading WhisperX model...")
        load_whisper_model()
        print("Model loaded successfully!")
    else:
        print("WhisperX not available, using mock transcription for demo")

    # 创建必要的目录
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("frontend", exist_ok=True)

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """返回前端页面"""
    try:
        async with aiofiles.open("frontend/index.html", mode='r', encoding='utf-8') as f:
            content = await f.read()
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(content="<h1>Frontend not found. Please check frontend/index.html</h1>")

@app.post("/api/upload")
async def upload_file(file: UploadFile = File(...), db: Session = Depends(get_db)):
    """上传并转录音频/视频文件"""
    
    # 检查文件类型
    allowed_extensions = {'.mp4', '.mp3', '.wav', '.m4a', '.flac', '.ogg', '.webm', '.avi', '.mov'}
    file_extension = os.path.splitext(file.filename)[1].lower()
    
    if file_extension not in allowed_extensions:
        raise HTTPException(status_code=400, detail="不支持的文件格式")
    
    # 生成唯一文件名
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join("uploads", unique_filename)
    
    try:
        # 保存上传的文件
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        file_size = len(content)
        
        # 使用WhisperX进行转录或模拟转录
        if WHISPERX_AVAILABLE:
            model = load_whisper_model()

            # 加载音频
            audio = whisperx.load_audio(file_path)

            # 转录
            result = model.transcribe(audio, batch_size=16)

            # 对齐文本（可选，提高准确性）
            model_a, metadata = whisperx.load_align_model(language_code=result["language"], device=device)
            result = whisperx.align(result["segments"], model_a, metadata, audio, device, return_char_alignments=False)

            # 计算音频时长
            duration = f"{len(audio) / 16000:.2f}s"  # WhisperX默认采样率16kHz
        else:
            # 使用模拟转录
            result = mock_transcribe(file_path)
            duration = "15.00s"  # 模拟时长

        # 格式化转录文本为Markdown
        transcription_text = format_transcription_to_markdown(result)
        
        # 保存到数据库
        db_transcription = Transcription(
            filename=unique_filename,
            original_filename=file.filename,
            transcription_text=transcription_text,
            file_size=file_size,
            duration=duration
        )
        db.add(db_transcription)
        db.commit()
        db.refresh(db_transcription)
        
        return {
            "id": db_transcription.id,
            "filename": file.filename,
            "transcription": transcription_text,
            "duration": duration,
            "created_at": db_transcription.created_at.isoformat()
        }
        
    except Exception as e:
        # 清理上传的文件
        if os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(status_code=500, detail=f"转录失败: {str(e)}")

def format_transcription_to_markdown(result):
    """将转录结果格式化为Markdown"""
    markdown_text = "# 转录结果\n\n"
    
    if "segments" in result:
        for i, segment in enumerate(result["segments"], 1):
            start_time = segment.get("start", 0)
            end_time = segment.get("end", 0)
            text = segment.get("text", "").strip()
            
            # 格式化时间戳
            start_min, start_sec = divmod(int(start_time), 60)
            end_min, end_sec = divmod(int(end_time), 60)
            
            markdown_text += f"## 片段 {i}\n"
            markdown_text += f"**时间:** {start_min:02d}:{start_sec:02d} - {end_min:02d}:{end_sec:02d}\n\n"
            markdown_text += f"{text}\n\n"
    else:
        # 如果没有分段信息，直接输出文本
        markdown_text += result.get("text", "转录失败")
    
    return markdown_text

@app.get("/api/history")
async def get_history(db: Session = Depends(get_db)):
    """获取转录历史记录"""
    transcriptions = db.query(Transcription).order_by(Transcription.created_at.desc()).limit(50).all()
    
    return [
        {
            "id": t.id,
            "filename": t.original_filename,
            "created_at": t.created_at.isoformat(),
            "duration": t.duration,
            "file_size": t.file_size
        }
        for t in transcriptions
    ]

@app.get("/api/transcription/{transcription_id}")
async def get_transcription(transcription_id: int, db: Session = Depends(get_db)):
    """获取特定转录记录"""
    transcription = db.query(Transcription).filter(Transcription.id == transcription_id).first()
    
    if not transcription:
        raise HTTPException(status_code=404, detail="转录记录未找到")
    
    return {
        "id": transcription.id,
        "filename": transcription.original_filename,
        "transcription": transcription.transcription_text,
        "created_at": transcription.created_at.isoformat(),
        "duration": transcription.duration,
        "file_size": transcription.file_size
    }

@app.delete("/api/transcription/{transcription_id}")
async def delete_transcription(transcription_id: int, db: Session = Depends(get_db)):
    """删除转录记录"""
    transcription = db.query(Transcription).filter(Transcription.id == transcription_id).first()
    
    if not transcription:
        raise HTTPException(status_code=404, detail="转录记录未找到")
    
    # 删除文件
    file_path = os.path.join("uploads", transcription.filename)
    if os.path.exists(file_path):
        os.remove(file_path)
    
    # 删除数据库记录
    db.delete(transcription)
    db.commit()
    
    return {"message": "转录记录已删除"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
