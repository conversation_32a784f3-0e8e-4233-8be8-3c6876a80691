from fastapi import FastAPI, File, UploadFile, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import os
import tempfile
import uuid
from datetime import datetime
from typing import List, Optional
import json
import subprocess
import requests
import base64
from sqlalchemy import create_engine, Column, String, DateTime, Text, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
import aiofiles

# 尝试导入WhisperX，如果失败则使用Ollama或模拟版本
try:
    import whisperx
    import torch
    WHISPERX_AVAILABLE = True
except ImportError:
    WHISPERX_AVAILABLE = False
    print("警告: WhisperX未安装，将尝试使用Ollama Whisper模型")

# Ollama配置
OLLAMA_BASE_URL = "http://localhost:11434"
OLLAMA_MODEL = "ZimaBlueAI/whisper-large-v3:latest"

# 数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./transcriptions.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# 数据库模型
class Transcription(Base):
    __tablename__ = "transcriptions"
    
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String, index=True)
    original_filename = Column(String)
    transcription_text = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    file_size = Column(Integer)
    duration = Column(String)

Base.metadata.create_all(bind=engine)

# 依赖注入
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

app = FastAPI(title="Audio/Video Transcription API", version="1.0.0")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
app.mount("/static", StaticFiles(directory="frontend"), name="static")

# 全局变量存储WhisperX模型
whisper_model = None
device = None

def load_whisper_model():
    """加载WhisperX模型"""
    global whisper_model, device
    if not WHISPERX_AVAILABLE:
        return None

    if whisper_model is None:
        device = "cuda" if torch.cuda.is_available() else "cpu"
        compute_type = "float16" if device == "cuda" else "int8"

        # 加载模型
        whisper_model = whisperx.load_model("base", device, compute_type=compute_type)

    return whisper_model

def check_ollama_available():
    """检查Ollama是否可用"""
    try:
        response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=5)
        return response.status_code == 200
    except:
        return False

def check_ffmpeg_available():
    """检查ffmpeg是否可用"""
    try:
        subprocess.run(["ffmpeg", "-version"], capture_output=True, check=True)
        return True
    except:
        return False

def extract_audio_with_ffmpeg(video_path, audio_path):
    """使用ffmpeg从视频中提取音频"""
    try:
        cmd = [
            "ffmpeg", "-i", video_path,
            "-vn", "-acodec", "pcm_s16le",
            "-ar", "16000", "-ac", "1",
            audio_path, "-y"
        ]
        subprocess.run(cmd, capture_output=True, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"ffmpeg错误: {e}")
        return False

def transcribe_with_whisper_cli(file_path):
    """使用whisper命令行工具进行转录"""
    try:
        # 如果是视频文件，先提取音频
        audio_path = file_path
        if file_path.lower().endswith(('.mp4', '.avi', '.mov', '.webm')):
            if not check_ffmpeg_available():
                raise Exception("需要安装ffmpeg来处理视频文件")

            audio_path = file_path.rsplit('.', 1)[0] + '_temp.wav'
            if not extract_audio_with_ffmpeg(file_path, audio_path):
                raise Exception("音频提取失败")

        # 使用正确的whisper命令行参数
        output_dir = os.path.dirname(audio_path)
        cmd = [
            "whisper", audio_path,
            "--language", "zh",
            "--output_format", "txt",
            "--output_dir", output_dir
        ]

        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=300
        )

        if result.returncode == 0:
            # 查找输出的txt文件
            base_name = os.path.splitext(os.path.basename(audio_path))[0]
            txt_file = os.path.join(output_dir, f"{base_name}.txt")

            transcription_text = ""
            if os.path.exists(txt_file):
                with open(txt_file, 'r', encoding='utf-8') as f:
                    transcription_text = f.read().strip()
                os.remove(txt_file)  # 清理txt文件
            else:
                # 如果没有txt文件，尝试从stdout获取
                transcription_text = result.stdout.strip()

            # 清理临时音频文件
            if audio_path != file_path and os.path.exists(audio_path):
                os.remove(audio_path)

            # 格式化为segments格式
            return {
                "language": "zh",
                "segments": [
                    {
                        "start": 0.0,
                        "end": 30.0,  # 估算时长
                        "text": transcription_text if transcription_text else "转录完成，但未获取到文本内容"
                    }
                ]
            }
        else:
            raise Exception(f"whisper命令执行失败: {result.stderr}")

    except Exception as e:
        # 清理临时文件
        if 'audio_path' in locals() and audio_path != file_path and os.path.exists(audio_path):
            os.remove(audio_path)
        raise e

def transcribe_with_ollama_direct(file_path):
    """直接使用ollama命令行调用Whisper模型"""
    try:
        # 如果是视频文件，先提取音频
        audio_path = file_path
        if file_path.lower().endswith(('.mp4', '.avi', '.mov', '.webm')):
            if not check_ffmpeg_available():
                raise Exception("需要安装ffmpeg来处理视频文件")

            audio_path = file_path.rsplit('.', 1)[0] + '_temp.wav'
            if not extract_audio_with_ffmpeg(file_path, audio_path):
                raise Exception("音频提取失败")

        # 使用ollama命令行直接调用模型
        # 注意：这里假设Ollama的Whisper模型支持文件路径输入
        cmd = ["ollama", "run", OLLAMA_MODEL, f"请转录音频文件: {audio_path}"]

        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=300,
            input=""  # 提供空输入
        )

        if result.returncode == 0:
            transcription_text = result.stdout.strip()

            # 清理临时音频文件
            if audio_path != file_path and os.path.exists(audio_path):
                os.remove(audio_path)

            # 格式化为segments格式
            return {
                "language": "zh",
                "segments": [
                    {
                        "start": 0.0,
                        "end": 30.0,  # 估算时长
                        "text": transcription_text if transcription_text else "Ollama返回了空结果"
                    }
                ]
            }
        else:
            raise Exception(f"Ollama命令执行失败: {result.stderr}")

    except Exception as e:
        # 清理临时文件
        if 'audio_path' in locals() and audio_path != file_path and os.path.exists(audio_path):
            os.remove(audio_path)
        raise e

def transcribe_with_ollama(file_path):
    """使用Ollama Whisper模型进行转录"""
    # 由于Ollama的Whisper模型可能需要特殊的集成方式
    # 我们先尝试使用whisper.cpp，然后回退到API方式
    try:
        return transcribe_with_whisper_cpp(file_path)
    except Exception as e1:
        print(f"whisper.cpp方式失败: {e1}")
        # 如果whisper.cpp失败，尝试API方式
        try:
            return transcribe_with_ollama_api(file_path)
        except Exception as e2:
            print(f"API方式也失败: {e2}")
            # 最后回退到模拟转录，但提供有用的错误信息
            return {
                "language": "zh",
                "segments": [
                    {
                        "start": 0.0,
                        "end": 30.0,
                        "text": f"Ollama Whisper模型转录失败。错误信息: whisper.cpp={str(e1)[:100]}, API={str(e2)[:100]}。请检查Ollama配置或安装whisper.cpp。"
                    }
                ]
            }

def mock_transcribe(file_path):
    """模拟转录功能，用于演示"""
    filename = os.path.basename(file_path)
    return {
        "language": "zh",
        "segments": [
            {
                "start": 0.0,
                "end": 5.0,
                "text": f"这是对文件 {filename} 的模拟转录结果。"
            },
            {
                "start": 5.0,
                "end": 10.0,
                "text": "由于WhisperX和Ollama都不可用，这里显示的是演示内容。"
            },
            {
                "start": 10.0,
                "end": 15.0,
                "text": "请安装WhisperX或配置Ollama以获得真实的语音识别功能。"
            }
        ]
    }

@app.on_event("startup")
async def startup_event():
    """应用启动时加载模型"""
    print("正在检查可用的转录服务...")

    if WHISPERX_AVAILABLE:
        print("✓ WhisperX可用，正在加载模型...")
        load_whisper_model()
        print("✓ WhisperX模型加载成功!")
    elif check_ollama_available():
        print("✓ Ollama服务可用，将使用Ollama Whisper模型")
        print(f"✓ 使用模型: {OLLAMA_MODEL}")
    else:
        print("⚠ WhisperX和Ollama都不可用，将使用模拟转录功能")

    # 检查ffmpeg
    if check_ffmpeg_available():
        print("✓ ffmpeg可用，支持视频文件处理")
    else:
        print("⚠ ffmpeg不可用，视频文件处理可能失败")

    # 创建必要的目录
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("frontend", exist_ok=True)

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """返回前端页面"""
    try:
        async with aiofiles.open("frontend/index.html", mode='r', encoding='utf-8') as f:
            content = await f.read()
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(content="<h1>Frontend not found. Please check frontend/index.html</h1>")

@app.post("/api/upload")
async def upload_file(file: UploadFile = File(...), db: Session = Depends(get_db)):
    """上传并转录音频/视频文件"""
    
    # 检查文件类型
    allowed_extensions = {'.mp4', '.mp3', '.wav', '.m4a', '.flac', '.ogg', '.webm', '.avi', '.mov'}
    file_extension = os.path.splitext(file.filename)[1].lower()
    
    if file_extension not in allowed_extensions:
        raise HTTPException(status_code=400, detail="不支持的文件格式")
    
    # 生成唯一文件名
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join("uploads", unique_filename)
    
    try:
        # 保存上传的文件
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        file_size = len(content)
        
        # 选择转录方法
        if WHISPERX_AVAILABLE:
            print("使用WhisperX进行转录...")
            model = load_whisper_model()

            # 加载音频
            audio = whisperx.load_audio(file_path)

            # 转录
            result = model.transcribe(audio, batch_size=16)

            # 对齐文本（可选，提高准确性）
            model_a, metadata = whisperx.load_align_model(language_code=result["language"], device=device)
            result = whisperx.align(result["segments"], model_a, metadata, audio, device, return_char_alignments=False)

            # 计算音频时长
            duration = f"{len(audio) / 16000:.2f}s"  # WhisperX默认采样率16kHz

        elif check_ollama_available():
            print("使用Ollama Whisper模型进行转录...")
            result = transcribe_with_ollama(file_path)
            duration = "30.00s"  # 估算时长

        else:
            print("使用模拟转录...")
            # 使用模拟转录
            result = mock_transcribe(file_path)
            duration = "15.00s"  # 模拟时长

        # 格式化转录文本为Markdown
        transcription_text = format_transcription_to_markdown(result)
        
        # 保存到数据库
        db_transcription = Transcription(
            filename=unique_filename,
            original_filename=file.filename,
            transcription_text=transcription_text,
            file_size=file_size,
            duration=duration
        )
        db.add(db_transcription)
        db.commit()
        db.refresh(db_transcription)
        
        return {
            "id": db_transcription.id,
            "filename": file.filename,
            "transcription": transcription_text,
            "duration": duration,
            "created_at": db_transcription.created_at.isoformat()
        }
        
    except Exception as e:
        # 清理上传的文件
        if os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(status_code=500, detail=f"转录失败: {str(e)}")

def format_transcription_to_markdown(result):
    """将转录结果格式化为Markdown"""
    markdown_text = "# 转录结果\n\n"
    
    if "segments" in result:
        for i, segment in enumerate(result["segments"], 1):
            start_time = segment.get("start", 0)
            end_time = segment.get("end", 0)
            text = segment.get("text", "").strip()
            
            # 格式化时间戳
            start_min, start_sec = divmod(int(start_time), 60)
            end_min, end_sec = divmod(int(end_time), 60)
            
            markdown_text += f"## 片段 {i}\n"
            markdown_text += f"**时间:** {start_min:02d}:{start_sec:02d} - {end_min:02d}:{end_sec:02d}\n\n"
            markdown_text += f"{text}\n\n"
    else:
        # 如果没有分段信息，直接输出文本
        markdown_text += result.get("text", "转录失败")
    
    return markdown_text

@app.get("/api/history")
async def get_history(db: Session = Depends(get_db)):
    """获取转录历史记录"""
    transcriptions = db.query(Transcription).order_by(Transcription.created_at.desc()).limit(50).all()
    
    return [
        {
            "id": t.id,
            "filename": t.original_filename,
            "created_at": t.created_at.isoformat(),
            "duration": t.duration,
            "file_size": t.file_size
        }
        for t in transcriptions
    ]

@app.get("/api/transcription/{transcription_id}")
async def get_transcription(transcription_id: int, db: Session = Depends(get_db)):
    """获取特定转录记录"""
    transcription = db.query(Transcription).filter(Transcription.id == transcription_id).first()
    
    if not transcription:
        raise HTTPException(status_code=404, detail="转录记录未找到")
    
    return {
        "id": transcription.id,
        "filename": transcription.original_filename,
        "transcription": transcription.transcription_text,
        "created_at": transcription.created_at.isoformat(),
        "duration": transcription.duration,
        "file_size": transcription.file_size
    }

@app.delete("/api/transcription/{transcription_id}")
async def delete_transcription(transcription_id: int, db: Session = Depends(get_db)):
    """删除转录记录"""
    transcription = db.query(Transcription).filter(Transcription.id == transcription_id).first()
    
    if not transcription:
        raise HTTPException(status_code=404, detail="转录记录未找到")
    
    # 删除文件
    file_path = os.path.join("uploads", transcription.filename)
    if os.path.exists(file_path):
        os.remove(file_path)
    
    # 删除数据库记录
    db.delete(transcription)
    db.commit()
    
    return {"message": "转录记录已删除"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
