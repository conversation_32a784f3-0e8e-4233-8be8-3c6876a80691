#!/usr/bin/env python3
"""
WhisperX安装脚本
"""

import subprocess
import sys
import platform

def install_whisperx():
    """安装WhisperX及其依赖"""
    print("正在安装WhisperX...")
    
    try:
        # 安装PyTorch (根据系统选择合适版本)
        system = platform.system().lower()
        if system == "darwin":  # macOS
            print("检测到macOS系统，安装CPU版本的PyTorch...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                "torch", "torchaudio"
            ])
        elif system == "linux":
            print("检测到Linux系统，尝试安装CUDA版本的PyTorch...")
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", 
                    "torch", "torchaudio", "--index-url", 
                    "https://download.pytorch.org/whl/cu118"
                ])
            except subprocess.CalledProcessError:
                print("CUDA版本安装失败，回退到CPU版本...")
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", 
                    "torch", "torchaudio", "--index-url", 
                    "https://download.pytorch.org/whl/cpu"
                ])
        else:  # Windows
            print("检测到Windows系统，安装CPU版本的PyTorch...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                "torch", "torchaudio"
            ])
        
        # 安装WhisperX
        print("安装WhisperX...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "whisperx"
        ])
        
        print("WhisperX安装完成!")
        print("现在可以使用真实的语音识别功能了。")
        
    except subprocess.CalledProcessError as e:
        print(f"安装失败: {e}")
        print("请尝试手动安装:")
        print("pip install torch torchaudio")
        print("pip install whisperx")
        sys.exit(1)

def check_whisperx():
    """检查WhisperX是否已安装"""
    try:
        import whisperx
        print("WhisperX已安装")
        return True
    except ImportError:
        print("WhisperX未安装")
        return False

def main():
    print("=" * 50)
    print("WhisperX安装工具")
    print("=" * 50)
    
    if check_whisperx():
        print("WhisperX已经安装，无需重复安装。")
        return
    
    response = input("是否要安装WhisperX? (y/N): ").lower().strip()
    if response in ['y', 'yes']:
        install_whisperx()
    else:
        print("跳过WhisperX安装。应用将使用模拟转录功能。")

if __name__ == "__main__":
    main()
