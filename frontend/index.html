<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音视频转文字工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #0056b3;
            background-color: #e3f2fd;
        }
        
        .upload-area.dragover {
            border-color: #28a745;
            background-color: #d4edda;
        }
        
        .progress-container {
            display: none;
            margin-top: 20px;
        }
        
        .transcription-result {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            max-height: 500px;
            overflow-y: auto;
        }
        
        .history-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .history-item:hover {
            background-color: #f8f9fa;
            border-color: #007bff;
        }
        
        .file-info {
            font-size: 0.9em;
            color: #6c757d;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .tab-content {
            margin-top: 20px;
        }
        
        .markdown-content {
            line-height: 1.6;
        }
        
        .markdown-content h1, .markdown-content h2, .markdown-content h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        
        .markdown-content p {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-microphone-alt text-primary"></i>
                    音视频转文字工具
                </h1>
            </div>
        </div>
        
        <!-- 导航标签 -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload" type="button" role="tab">
                    <i class="fas fa-upload"></i> 上传转录
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab">
                    <i class="fas fa-history"></i> 历史记录
                </button>
            </li>
        </ul>
        
        <div class="tab-content" id="mainTabContent">
            <!-- 上传转录标签页 -->
            <div class="tab-pane fade show active" id="upload" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="upload-area" id="uploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                            <h4>拖拽文件到此处或点击选择</h4>
                            <p class="text-muted">支持 MP4, MP3, WAV, M4A, FLAC, OGG, WebM, AVI, MOV 格式</p>
                            <input type="file" id="fileInput" class="d-none" accept=".mp4,.mp3,.wav,.m4a,.flac,.ogg,.webm,.avi,.mov">
                        </div>
                        
                        <div class="progress-container" id="progressContainer">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span id="progressText">准备上传...</span>
                                <div class="loading-spinner" id="loadingSpinner">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                </div>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <div class="mt-3" id="fileInfo" style="display: none;">
                            <div class="alert alert-info">
                                <strong>文件信息:</strong>
                                <div id="fileDetails"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="transcription-result" id="transcriptionResult" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="fas fa-file-alt"></i> 转录结果</h5>
                                <button class="btn btn-sm btn-outline-primary" id="copyBtn">
                                    <i class="fas fa-copy"></i> 复制
                                </button>
                            </div>
                            <div class="markdown-content" id="transcriptionContent"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 历史记录标签页 -->
            <div class="tab-pane fade" id="history" role="tabpanel">
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5><i class="fas fa-history"></i> 历史记录</h5>
                            <button class="btn btn-sm btn-outline-primary" id="refreshHistoryBtn">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                        <div id="historyList">
                            <div class="text-center text-muted">
                                <i class="fas fa-spinner fa-spin"></i> 加载中...
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-8">
                        <div class="transcription-result" id="historyTranscriptionResult" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="fas fa-file-alt"></i> <span id="historyFileName"></span></h5>
                                <div>
                                    <button class="btn btn-sm btn-outline-primary" id="copyHistoryBtn">
                                        <i class="fas fa-copy"></i> 复制
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" id="deleteHistoryBtn">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </div>
                            </div>
                            <div class="file-info mb-3" id="historyFileInfo"></div>
                            <div class="markdown-content" id="historyTranscriptionContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Toast 通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <strong class="me-auto">通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastBody"></div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="static/app.js"></script>
</body>
</html>
