class TranscriptionApp {
    constructor() {
        this.currentTranscriptionId = null;
        this.initializeEventListeners();
        this.loadHistory();
    }

    initializeEventListeners() {
        // 文件上传相关
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');

        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        fileInput.addEventListener('change', this.handleFileSelect.bind(this));

        // 按钮事件
        document.getElementById('copyBtn').addEventListener('click', this.copyTranscription.bind(this));
        document.getElementById('copyHistoryBtn').addEventListener('click', this.copyHistoryTranscription.bind(this));
        document.getElementById('deleteHistoryBtn').addEventListener('click', this.deleteHistoryItem.bind(this));
        document.getElementById('refreshHistoryBtn').addEventListener('click', this.loadHistory.bind(this));

        // 标签页切换事件
        document.getElementById('history-tab').addEventListener('shown.bs.tab', this.loadHistory.bind(this));
    }

    handleDragOver(e) {
        e.preventDefault();
        document.getElementById('uploadArea').classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        document.getElementById('uploadArea').classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        document.getElementById('uploadArea').classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }

    handleFileSelect(e) {
        const files = e.target.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }

    processFile(file) {
        // 验证文件类型
        const allowedTypes = [
            'video/mp4', 'audio/mp3', 'audio/wav', 'audio/m4a', 
            'audio/flac', 'audio/ogg', 'video/webm', 'video/avi', 'video/quicktime'
        ];
        
        const allowedExtensions = ['.mp4', '.mp3', '.wav', '.m4a', '.flac', '.ogg', '.webm', '.avi', '.mov'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
            this.showToast('不支持的文件格式', 'error');
            return;
        }

        // 显示文件信息
        this.showFileInfo(file);
        
        // 开始上传
        this.uploadFile(file);
    }

    showFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileDetails = document.getElementById('fileDetails');
        
        const sizeInMB = (file.size / (1024 * 1024)).toFixed(2);
        fileDetails.innerHTML = `
            <div><strong>文件名:</strong> ${file.name}</div>
            <div><strong>大小:</strong> ${sizeInMB} MB</div>
            <div><strong>类型:</strong> ${file.type || '未知'}</div>
        `;
        
        fileInfo.style.display = 'block';
    }

    async uploadFile(file) {
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const loadingSpinner = document.getElementById('loadingSpinner');
        
        progressContainer.style.display = 'block';
        loadingSpinner.style.display = 'block';
        
        const formData = new FormData();
        formData.append('file', file);

        try {
            progressText.textContent = '上传中...';
            progressBar.style.width = '30%';

            const response = await fetch('/api/upload', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || '上传失败');
            }

            progressText.textContent = '转录中...';
            progressBar.style.width = '70%';

            const result = await response.json();
            
            progressText.textContent = '完成!';
            progressBar.style.width = '100%';
            
            setTimeout(() => {
                progressContainer.style.display = 'none';
                loadingSpinner.style.display = 'none';
                progressBar.style.width = '0%';
            }, 1000);

            this.displayTranscription(result);
            this.showToast('转录完成!', 'success');
            
        } catch (error) {
            console.error('Upload error:', error);
            progressContainer.style.display = 'none';
            loadingSpinner.style.display = 'none';
            this.showToast(`转录失败: ${error.message}`, 'error');
        }
    }

    displayTranscription(result) {
        const transcriptionResult = document.getElementById('transcriptionResult');
        const transcriptionContent = document.getElementById('transcriptionContent');
        
        // 使用marked.js渲染Markdown
        transcriptionContent.innerHTML = marked.parse(result.transcription);
        transcriptionResult.style.display = 'block';
        
        // 保存当前转录ID
        this.currentTranscriptionId = result.id;
    }

    async loadHistory() {
        const historyList = document.getElementById('historyList');
        
        try {
            historyList.innerHTML = '<div class="text-center text-muted"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>';
            
            const response = await fetch('/api/history');
            if (!response.ok) {
                throw new Error('加载历史记录失败');
            }
            
            const history = await response.json();
            
            if (history.length === 0) {
                historyList.innerHTML = '<div class="text-center text-muted">暂无历史记录</div>';
                return;
            }
            
            historyList.innerHTML = history.map(item => `
                <div class="history-item" data-id="${item.id}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${item.filename}</h6>
                            <div class="file-info">
                                <small><i class="fas fa-clock"></i> ${this.formatDate(item.created_at)}</small><br>
                                <small><i class="fas fa-stopwatch"></i> ${item.duration}</small>
                                <small><i class="fas fa-file"></i> ${this.formatFileSize(item.file_size)}</small>
                            </div>
                        </div>
                        <i class="fas fa-chevron-right text-muted"></i>
                    </div>
                </div>
            `).join('');
            
            // 添加点击事件
            document.querySelectorAll('.history-item').forEach(item => {
                item.addEventListener('click', () => {
                    const id = item.dataset.id;
                    this.loadTranscription(id);
                });
            });
            
        } catch (error) {
            console.error('Load history error:', error);
            historyList.innerHTML = '<div class="text-center text-danger">加载失败</div>';
        }
    }

    async loadTranscription(id) {
        try {
            const response = await fetch(`/api/transcription/${id}`);
            if (!response.ok) {
                throw new Error('加载转录记录失败');
            }
            
            const transcription = await response.json();
            this.displayHistoryTranscription(transcription);
            
        } catch (error) {
            console.error('Load transcription error:', error);
            this.showToast('加载转录记录失败', 'error');
        }
    }

    displayHistoryTranscription(transcription) {
        const historyResult = document.getElementById('historyTranscriptionResult');
        const historyContent = document.getElementById('historyTranscriptionContent');
        const historyFileName = document.getElementById('historyFileName');
        const historyFileInfo = document.getElementById('historyFileInfo');
        
        historyFileName.textContent = transcription.filename;
        historyFileInfo.innerHTML = `
            <small><i class="fas fa-clock"></i> ${this.formatDate(transcription.created_at)}</small>
            <small><i class="fas fa-stopwatch"></i> ${transcription.duration}</small>
            <small><i class="fas fa-file"></i> ${this.formatFileSize(transcription.file_size)}</small>
        `;
        
        historyContent.innerHTML = marked.parse(transcription.transcription);
        historyResult.style.display = 'block';
        
        // 保存当前历史记录ID
        this.currentHistoryId = transcription.id;
    }

    async deleteHistoryItem() {
        if (!this.currentHistoryId) {
            this.showToast('请先选择要删除的记录', 'error');
            return;
        }
        
        if (!confirm('确定要删除这条转录记录吗？')) {
            return;
        }
        
        try {
            const response = await fetch(`/api/transcription/${this.currentHistoryId}`, {
                method: 'DELETE'
            });
            
            if (!response.ok) {
                throw new Error('删除失败');
            }
            
            this.showToast('删除成功', 'success');
            document.getElementById('historyTranscriptionResult').style.display = 'none';
            this.loadHistory();
            
        } catch (error) {
            console.error('Delete error:', error);
            this.showToast('删除失败', 'error');
        }
    }

    copyTranscription() {
        const content = document.getElementById('transcriptionContent');
        this.copyToClipboard(content.textContent);
    }

    copyHistoryTranscription() {
        const content = document.getElementById('historyTranscriptionContent');
        this.copyToClipboard(content.textContent);
    }

    copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            this.showToast('已复制到剪贴板', 'success');
        }).catch(() => {
            this.showToast('复制失败', 'error');
        });
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showToast(message, type = 'info') {
        const toast = document.getElementById('toast');
        const toastBody = document.getElementById('toastBody');
        
        toastBody.textContent = message;
        
        // 设置toast样式
        toast.className = `toast ${type === 'error' ? 'bg-danger text-white' : type === 'success' ? 'bg-success text-white' : ''}`;
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new TranscriptionApp();
});
