# 音视频转文字工具

一个基于WhisperX的音视频转文字工具，支持前后端分离架构，提供Web界面进行文件上传和转录。

## 功能特性

- 🎵 **多格式支持**: 支持MP4, MP3, WAV, M4A, FLAC, OGG, WebM, AVI, MOV等主流音视频格式
- 🤖 **高精度识别**: 使用WhisperX进行语音识别，支持多语言
- 📝 **Markdown输出**: 转录结果以Markdown格式输出并实时渲染
- 📚 **历史记录**: 自动保存转录历史，支持查看、复制和删除
- 🎨 **现代界面**: 响应式Web界面，支持拖拽上传
- ⚡ **实时进度**: 显示上传和转录进度
- 💾 **数据持久化**: 使用SQLite数据库存储历史记录

## 系统要求

- Python 3.8+
- 4GB+ RAM (推荐8GB+)
- 支持CUDA的GPU (可选，用于加速)

## 快速开始

### 方法一：使用启动脚本（推荐）

1. **下载项目**
   ```bash
   git clone <repository-url>
   cd mp4convert
   ```

2. **运行启动脚本**

   **Windows:**
   ```bash
   start.bat
   ```

   **Linux/Mac:**
   ```bash
   ./start.sh
   ```

   **或者使用Python:**
   ```bash
   python start.py
   ```

3. **安装WhisperX（可选）**

   首次运行会使用模拟转录功能。要使用真实的语音识别，请运行：
   ```bash
   python install_whisperx.py
   ```

4. **访问应用**

   打开浏览器访问: http://localhost:8000

### 方法二：手动安装

1. **安装基础依赖**
   ```bash
   pip install fastapi "uvicorn[standard]" python-multipart sqlalchemy aiofiles python-dotenv
   ```

2. **安装WhisperX（可选）**
   ```bash
   pip install torch torchaudio
   pip install whisperx
   ```

3. **启动应用**
   ```bash
   uvicorn backend.main:app --host 0.0.0.0 --port 8000 --reload
   ```

## 项目结构

```
mp4convert/
├── backend/
│   └── main.py              # FastAPI后端应用
├── frontend/
│   ├── index.html           # 前端页面
│   └── app.js              # 前端JavaScript
├── uploads/                 # 上传文件存储目录
├── requirements.txt         # Python依赖
├── start.py                # 启动脚本
└── README.md               # 项目说明
```

## API接口

### 上传转录
- **POST** `/api/upload`
- 上传音视频文件并进行转录

### 获取历史记录
- **GET** `/api/history`
- 获取转录历史记录列表

### 获取转录详情
- **GET** `/api/transcription/{id}`
- 获取特定转录记录的详细信息

### 删除转录记录
- **DELETE** `/api/transcription/{id}`
- 删除指定的转录记录

## 使用说明

### 上传文件
1. 在"上传转录"标签页中，点击上传区域或拖拽文件到上传区域
2. 支持的文件格式：MP4, MP3, WAV, M4A, FLAC, OGG, WebM, AVI, MOV
3. 系统会自动显示上传进度和转录进度
4. 转录完成后，结果会以Markdown格式显示

### 查看历史记录
1. 切换到"历史记录"标签页
2. 点击任意历史记录查看详细内容
3. 可以复制转录内容或删除记录

### 转录结果格式
转录结果采用Markdown格式，包含：
- 标题和时间戳
- 分段文本内容
- 清晰的层次结构

## 配置说明

### WhisperX模型配置
默认使用`base`模型，可在`backend/main.py`中修改：

```python
# 在load_whisper_model函数中修改模型大小
whisper_model = whisperx.load_model("large-v2", device, compute_type=compute_type)
```

可选模型：
- `tiny`: 最快，精度较低
- `base`: 平衡速度和精度
- `small`: 较好精度
- `medium`: 更好精度
- `large-v2`: 最高精度，速度较慢

### GPU加速
如果有NVIDIA GPU，确保安装了CUDA版本的PyTorch：

```bash
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## 故障排除

### 常见问题

1. **模型下载失败**
   - 确保网络连接正常
   - 可能需要科学上网下载模型文件

2. **内存不足**
   - 使用较小的模型（如`tiny`或`base`）
   - 减少batch_size参数

3. **转录速度慢**
   - 使用GPU加速
   - 选择较小的模型

4. **文件上传失败**
   - 检查文件格式是否支持
   - 确保文件大小合理

### 日志查看
应用运行时会在控制台输出详细日志，包括：
- 模型加载状态
- 文件处理进度
- 错误信息

## 开发说明

### 后端开发
- 基于FastAPI框架
- 使用SQLAlchemy进行数据库操作
- 异步文件处理

### 前端开发
- 原生JavaScript + Bootstrap
- 使用marked.js进行Markdown渲染
- 响应式设计

### 扩展功能
可以考虑添加的功能：
- 用户认证系统
- 批量文件处理
- 更多输出格式
- 云存储集成
- 实时语音转录

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
