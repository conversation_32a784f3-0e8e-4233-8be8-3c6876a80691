#!/bin/bash

echo "================================================"
echo "音视频转文字工具 - Linux/Mac启动脚本"
echo "================================================"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.8+"
    exit 1
fi

# 检查Python版本
python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "错误: Python版本过低，需要Python 3.8+，当前版本: $python_version"
    exit 1
fi

# 启动应用
python3 start.py "$@"
