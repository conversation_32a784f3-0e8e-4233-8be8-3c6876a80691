#!/usr/bin/env python3
"""
项目演示脚本
"""

import subprocess
import sys
import time
import webbrowser
import threading
import os

def start_server():
    """启动服务器"""
    print("正在启动服务器...")
    try:
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", "backend.main:app", 
            "--host", "0.0.0.0", "--port", "8000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待服务器启动
        time.sleep(3)
        
        return process
    except Exception as e:
        print(f"启动服务器失败: {e}")
        return None

def open_browser():
    """打开浏览器"""
    time.sleep(2)  # 等待服务器完全启动
    try:
        webbrowser.open("http://localhost:8000")
        print("已在浏览器中打开应用")
    except Exception as e:
        print(f"无法自动打开浏览器: {e}")
        print("请手动访问: http://localhost:8000")

def show_demo_info():
    """显示演示信息"""
    print("""
=" * 60)
🎵 音视频转文字工具演示
=" * 60)

功能特性:
✅ 支持多种音视频格式 (MP4, MP3, WAV, M4A, FLAC, OGG, WebM, AVI, MOV)
✅ 拖拽上传文件
✅ 实时转录进度显示
✅ Markdown格式输出
✅ 历史记录管理
✅ 响应式Web界面

演示说明:
- 当前使用模拟转录功能进行演示
- 要使用真实语音识别，请运行: python install_whisperx.py
- 支持的文件格式会显示模拟转录结果
- 可以测试上传、查看历史记录等功能

使用方法:
1. 在"上传转录"页面拖拽或选择文件
2. 等待转录完成
3. 查看Markdown格式的转录结果
4. 在"历史记录"页面管理之前的转录

按 Ctrl+C 停止演示
""")

def main():
    """主函数"""
    show_demo_info()
    
    # 检查依赖
    try:
        import fastapi
        import uvicorn
    except ImportError:
        print("错误: 缺少必要依赖，请先运行: python start.py --install-deps")
        sys.exit(1)
    
    # 启动服务器
    server_process = start_server()
    if not server_process:
        sys.exit(1)
    
    # 在新线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # 等待用户中断
        server_process.wait()
    except KeyboardInterrupt:
        print("\n正在停止服务器...")
        server_process.terminate()
        server_process.wait()
        print("演示结束")

if __name__ == "__main__":
    main()
